package com.gwm.ailab.service.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.common.ResponseResult;
import com.gwm.ailab.service.util.OkHttpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * 生成式卡片控制器
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/card")
@Validated
@Tag(name = "服务卡片相关的API接口", description = "服务卡片相关的API接口")
@RequiredArgsConstructor
public class GenerativeCardController {

    private final OkHttpUtil okHttpUtil;

    /**
     * 生成式卡片接口
     *
     * @return SseEmitter 服务端推送事件流
     * <AUTHOR> Lab GW00295473
     * @since 1.0.0
     */
    @PostMapping(value = "/stream/generate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(
            summary = "生成式卡片生成接口",
            description = "通过EventStream流式返回生成服务卡片内容"
    )
    @ApiResponse(
            responseCode = "200",
            description = "成功建立SSE连接，开始流式返回生成结果。",
            content = @Content(mediaType = "text/event-stream")
    )
    public SseEmitter generate(@RequestBody JSONObject json) {

        // 创建SSE发射器，设置超时时间为5分钟
        SseEmitter sseEmitter = new SseEmitter(5 * 60 * 1000L);

        try {
            // 构建远程服务URL
            String remoteUrl = "http://generative-card-agent:8000/generative-card-agent/stream/llm_card";

            // 添加必要的请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "text/event-stream");

            // 通过EventStream调用远程服务
            okHttpUtil.eventStream(remoteUrl, json, sseEmitter, headers);

            log.warn("EventStream连接已建立，开始接收数据流");

        } catch (Exception e) {
            log.error("生成服务卡片失败", e);
            sseEmitter.completeWithError(e);
        }
        return sseEmitter;
    }

    /**
     * 生成式卡片接口（普通HTTP请求）
     *
     * @param json 请求参数
     * @return ResponseResult<String> 生成结果
     * <AUTHOR> Lab
     * @since 1.0.0
     */
    @PostMapping(value = "/generate", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "生成式卡片生成接口（普通HTTP）",
            description = "通过普通HTTP POST请求生成服务卡片内容"
    )
    @ApiResponse(
            responseCode = "200",
            description = "成功生成卡片内容",
            content = @Content(mediaType = "application/json")
    )
    public ResponseResult<String> generateSync(
            @RequestBody JSONObject json) {

        try {
            // 构建远程服务URL
//            String remoteUrl = "http://generative-card-agent:8000/generative-card-agent/llm_card";
            String remoteUrl = "http://bigdata-qa-service2.beantechyun.com:30004/generative-card-agent/llm_card";

            // 添加必要的请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");

            log.debug("最终发送的请求头: {}", headers);

            // 通过OkHttp调用远程服务
            String response = okHttpUtil.post(remoteUrl, json, headers);

            log.info("生成服务卡片成功，响应长度: {}", response != null ? response.length() : 0);

            return ResponseResult.success("生成成功", JSON.parseObject(response));

        } catch (Exception e) {
            log.error("生成服务卡片失败", e);
            return ResponseResult.error(500, "生成失败: " + e.getMessage());
        }
    }

}
